#!/usr/bin/env bash
set -e

# <PERSON><PERSON><PERSON> đường dẫn tuyệt đối của thư mục chứa script này
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Kill any process that is using port 55555
echo "Checking for process on port 55555 and killing it if found..."
fuser -k 55555/tcp || true

# Create virtual environment if it doesn't exist
if [ ! -d "$DIR/.venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv "$DIR/.venv"
fi

# Activate virtual environment and install dependencies
echo "Activating virtual environment and installing dependencies..."
source "$DIR/.venv/bin/activate"
pip install -r "$DIR/requirements.txt"

# Run the application using absolute paths
echo "Starting the application..."
exec "$DIR/.venv/bin/python" "$DIR/app.py"
