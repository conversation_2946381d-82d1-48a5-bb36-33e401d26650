<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>Send Encoded SQL</title>
  <style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        background-color: #f8f9fa;
        color: #212529;
        margin: 0;
        padding: 20px;
    }
    .container {
        max-width: 900px;
        margin: 0 auto;
        background-color: #ffffff;
        padding: 25px 30px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    h3 {
        text-align: center;
        color: #343a40;
        margin-bottom: 25px;
    }
    textarea {
        width: 100%;
        padding: 10px;
        font-family: monospace;
        font-size: 14px;
        border: 1px solid #ced4da;
        border-radius: 5px;
        box-sizing: border-box;
        resize: vertical;
    }
    input[type="password"] {
        width: 100%;
        padding: 10px;
        font-size: 14px;
        border: 1px solid #ced4da;
        border-radius: 5px;
        box-sizing: border-box;
    }
    button {
        display: block;
        width: 100%;
        padding: 12px 20px;
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        background-color: #007bff;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
    }
    button:hover {
        background-color: #0056b3;
    }
    pre {
        background-color: #e9ecef;
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 13px;
    }
    .row {
        margin-bottom: 20px;
    }
    label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }
    th, td {
        border: 1px solid #dee2e6;
        padding: 10px;
        text-align: left;
    }
    td {
        max-width: 250px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative; /* Required for tooltip */
    }
    thead tr {
        background-color: #f1f3f5;
    }
    .errors pre {
        background-color: #fff3f3;
        color: #d9534f;
        border-color: #d9534f;
    }
    .table-container {
        overflow-x: auto;
    }
    /* Custom Tooltip Styles */
    .custom-tooltip {
        visibility: hidden; /* Controlled by JS */
        opacity: 0; /* Controlled by JS */
        position: absolute;
        background-color: #333;
        color: #fff;
        padding: 10px;
        border-radius: 6px;
        z-index: 10;
        bottom: 105%;
        left: 50%;
        transform: translateX(-50%);
        transition: opacity 0.2s, visibility 0.2s;
        width: auto;
        max-width: 400px;
        white-space: normal;
        word-wrap: break-word;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        pointer-events: auto; /* Allow interaction */
    }
    .custom-tooltip span {
        display: block;
        margin-bottom: 8px;
    }
    .custom-tooltip button {
        padding: 4px 8px;
        font-size: 12px;
        background-color: #007bff;
        border: none;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        width: auto;
    }
    .custom-tooltip button:hover {
        background-color: #0056b3;
    }
  </style>
</head>
<body>
  <div class="container">
    <h3>Send Encoded SQL</h3>
    <form method="post" action="/send">
      <div class="row">
        <label>SQL</label>
          <br>
        <textarea name="input_sql" id="sql-input" rows="10">{{input_sql}}</textarea>
      </div>

      <div class="row" id="admin-password-row" style="display: none;">
        <label>Admin Password</label>
        <input type="password" name="admin_password" />
      </div>

      <div class="row">
        <button type="submit">Send</button>
      </div>

      {% if encoded_preview %}
      <div class="row">
        <label>Encoded Sent (Preview):</label>
        <pre>{{encoded_preview}}</pre>
      </div>
      {% endif %}

      <div class="row">
        <label>API Result:</label>
        <pre>{{api_result}}</pre>
      </div>

      {% if table_headers %}
      <div class="row">
          <label>Data Result:</label>
          <div class="table-container">
              <table>
                  <thead>
                      <tr>
                          {% for header in table_headers %}
                              <th>{{ header }}</th>
                          {% endfor %}
                      </tr>
                  </thead>
                  <tbody>
                      {% for row in table_rows %}
                          <tr>
                              {% for header in table_headers %}
                                  <td>{{ row[header] }}</td>
                              {% endfor %}
                          </tr>
                      {% endfor %}
                  </tbody>
              </table>
          </div>
      </div>
      {% endif %}

      {% if errors %}
      <div class="row errors">
          <label>Errors:</label>
          <pre>{{ errors }}</pre>
      </div>
      {% endif %}
    </form>
  </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sqlInput = document.getElementById('sql-input');
    const passwordRow = document.getElementById('admin-password-row');

    function checkQuery() {
        const query = sqlInput.value.trim().toLowerCase();
        if (query && !query.startsWith('select')) {
            passwordRow.style.display = 'block';
        } else {
            passwordRow.style.display = 'none';
        }
    }

    sqlInput.addEventListener('input', checkQuery);
    checkQuery(); // Run on page load

    const cells = document.querySelectorAll('tbody td');

    cells.forEach(cell => {
        if (cell.offsetWidth < cell.scrollWidth) {
            const tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip';
            
            const text = document.createElement('span');
            text.textContent = cell.textContent;
            
            const copyButton = document.createElement('button');
            copyButton.textContent = 'Copy';
            copyButton.onclick = function(e) {
                e.stopPropagation();
                navigator.clipboard.writeText(cell.textContent).then(() => {
                    copyButton.textContent = 'Copied!';
                    setTimeout(() => { copyButton.textContent = 'Copy'; }, 2000);
                });
            };

            tooltip.appendChild(text);
            tooltip.appendChild(copyButton);
            cell.appendChild(tooltip);

            let hideTimeout;

            const showTooltip = () => {
                clearTimeout(hideTimeout);
                tooltip.style.visibility = 'visible';
                tooltip.style.opacity = '1';
            };

            const hideTooltip = () => {
                hideTimeout = setTimeout(() => {
                    tooltip.style.visibility = 'hidden';
                    tooltip.style.opacity = '0';
                }, 200);
            };

            cell.addEventListener('mouseenter', showTooltip);
            cell.addEventListener('mouseleave', hideTooltip);
            tooltip.addEventListener('mouseenter', showTooltip);
            tooltip.addEventListener('mouseleave', hideTooltip);
        }
    });
});
</script>

</body>
</html>