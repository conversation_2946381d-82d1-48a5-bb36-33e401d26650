from flask import Flask, render_template, request, session, redirect, url_for
import re, base64, os, requests, json
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

app = Flask(__name__)
app.secret_key = os.urandom(24) # Cần thiết để sử dụng session

# ======== Tham số khớp AESUtil.java ========
GCM_TAG_LENGTH_BITS = 128
IV_LEN = 12
SALT_LEN = 16
ITERATIONS = 65536
KEY_LEN_BITS = 256

ADMIN_PASSWORD = "1757962186374"

def derive_key(password: str, salt: bytes) -> bytes:
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=KEY_LEN_BITS // 8,
        salt=salt,
        iterations=ITERATIONS,
    )
    return kdf.derive(password.encode())

def encrypt_java_compatible(plain_text: str, password: str) -> str:
    salt = os.urandom(SALT_LEN)
    iv = os.urandom(IV_LEN)
    key = derive_key(password, salt)
    aesgcm = AESGCM(key)
    ct_tag = aesgcm.encrypt(iv, plain_text.encode(), None)
    combined = salt + iv + ct_tag
    return base64.b64encode(combined).decode()

# ===========================================

DEFAULT_REGEX = r"ENC\('([^']*)'\)"  # group(1) là chuỗi đã encode

API_URL = "https://api.vipomart.vn/authen/query"
HEADERS = {
    "X-Internal-Secret": "Nv82Lx7Kq!Dfj29slPq2R5mxVfLzXp9A",
    "User-Agent": "Apidog/1.0.0 (https://apidog.com)",
    "Content-Type": "application/json",
    "Cookie": "JSESSIONID=F17B9DB321617A561FEC4966FDEFDBD3; SERVERID=A; JSESSIONID=D61C911FBB86F7CCF49CEF37540D0877; JSESSIONID=AA2727DFD8B4C62D662DA0CEEE4513D6; JSESSIONID=EBB42002922B3897226A984AED13E5B9",
}

@app.route("/", methods=["GET"])
def index():
    # Lấy kết quả từ session nếu có, sau đó xóa đi
    results = session.pop('results', None)
    if results:
        return render_template("index.html", **results)
    else:
        return render_template("index.html",
                               input_sql="",
                               regex=DEFAULT_REGEX,
                               password="test",
                               api_result="",
                               encoded_preview="",
                               errors="",
                               table_headers=None, table_rows=None)

@app.route("/send", methods=["POST"])
def send():
    sql = request.form.get("input_sql", "").strip()
    regex = request.form.get("regex", DEFAULT_REGEX)
    password = "test" # Mật khẩu mặc định cho mã hóa

    # Chuẩn bị để lưu kết quả vào session
    results = {
        "input_sql": sql,
        "regex": regex,
        "password": password,
        "api_result": "",
        "encoded_preview": "",
        "errors": "",
        "table_headers": None,
        "table_rows": None
    }

    # Kiểm tra mật khẩu admin cho các câu lệnh nguy hiểm
    if not sql.lower().lstrip().startswith('select'):
        admin_password = request.form.get("admin_password")
        if admin_password != ADMIN_PASSWORD:
            results['errors'] = "Incorrect admin password for non-SELECT query."
            session['results'] = results
            return redirect(url_for('index'))

    encoded = None
    errors = ""

    try:
        pat = re.compile(regex)
        m = pat.search(sql or "")
        if m:
            encoded = m.group(1)
    except re.error as e:
        errors = f"Invalid regex: {e}"

    if not errors and not encoded:
        if not sql:
            errors = "SQL input is empty, cannot auto-encrypt."
        else:
            try:
                encoded = encrypt_java_compatible(sql, password)
            except Exception as ex:
                errors = f"Auto-encryption failed: {ex}"

    if errors:
        results['errors'] = errors
        session['results'] = results
        return redirect(url_for('index'))

    body = {"encodedSql": encoded, "password": password}
    api_result = ""
    table_headers, table_rows = None, None

    try:
        resp = requests.post(API_URL, headers=HEADERS, json=body, timeout=30)
        api_result = f"HTTP {resp.status_code}\n\n"

        try:
            data = resp.json()
            if isinstance(data, dict) and 'data' in data and isinstance(data['data'], list) and data['data']:
                table_rows = data['data']
                table_headers = list(table_rows[0].keys())
                del data['data']
            api_result += json.dumps(data, indent=2)
        except json.JSONDecodeError:
            api_result += resp.text

    except Exception as ex:
        api_result = f"ERR calling API: {ex}"

    results.update({
        'encoded_preview': encoded,
        'api_result': api_result,
        'table_headers': table_headers,
        'table_rows': table_rows
    })

    session['results'] = results
    return redirect(url_for('index'))

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=55555, debug=True)